<html>

<head>
    <title>Telemidia - Contatos</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="icon" type="image/x-icon" href="./img/favicon_telemidia.ico" />

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
        integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.7.1.slim.min.js"
        integrity="sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=" crossorigin="anonymous"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow-x: hidden;
        }

        h5 {
            color: #FFF;
        }

        .phones-container {
            margin-top: 30px;
            color: #FFF;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .phones-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .phone-number {
            background: rgba(255, 255, 255, 0.15);
            padding: 12px 16px;
            border-radius: 8px;
            margin: 8px 0;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .phone-number:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            padding: 5px 0px;
            background: rgba(0, 0, 0, 0.5);
            color: #FFF;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10pt;
        }

        .outer-container {
            width: 100vw;
            min-height: 100vh;
            height: auto;
            background: linear-gradient(to bottom, #000084, #2048b7);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 0;
        }

        .main-container {
            height: auto;
            padding-bottom: 40px;
            width: 300px;
            margin: 0 auto;
            text-align: center;
        }

        img.logo {
            width: 200px;
            margin: 0 auto;
            margin-top: 20px;
            margin-bottom: 40px;
        }

        .contact-item {
            margin: 20px 0px;
        }

        .contact-item table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .contact-item td {
            text-align: center;
            color: #FFF;
            transition: background-color 0.3s, color 0.3s;
        }

        .contact-item:hover td {
            background: #FFF;
            color: #000;
            cursor: pointer;
        }

        .contact-item:hover td:last-child {
            border-left: 1px solid #DDD;
        }

        .contact-item td:first-child {
            padding: 10px;
            width: 40px;
            background: #FFF;
            border: 1px solid #FFF;
            border-radius: 8px 0px 0px 8px;
        }

        .contact-item td:last-child {
            font-weight: bold;
            border: 1px solid #FFF;
            border-radius: 0px 8px 8px 0px;
        }

        .contact-item td:first-child img {
            width: 30px;
        }
    </style>

    <script>
        $(document).ready(function () {
            $('.contact-item').on('click', function () {
                if ($(this).hasClass('website')) {
                    window.location.href = 'https://www.telemidia.net.br/'
                }
                else if ($(this).hasClass('whatsapp')) {
                    window.location.href = 'https://api.whatsapp.com/send?phone=553537311118'
                }
                else if ($(this).hasClass('facebook')) {
                    window.location.href = 'https://www.facebook.com/telemidia.fibra'
                }
                else if ($(this).hasClass('instagram')) {
                    window.location.href = 'https://www.instagram.com/telemidia.fibra'
                }
                else if ($(this).hasClass('linkedin')) {
                    window.location.href = 'https://www.linkedin.com/company/telemidia'
                }
            });
        });
    </script>
</head>

<body>
    <div class="outer-container">
        <div class="main-container">
            <img class="logo" src="./img/logo.png" />

            <h5>Escolha a melhor forma para você conversar com a Telemidia!</h5>

            <div class="contact-item whatsapp">
                <table>
                    <tr>
                        <td>
                            <img src="./img/whatsapp_icon.png" />
                        </td>

                        <td>
                            WhatsApp
                        </td>
                    </tr>
                </table>
            </div>

            <div class="contact-item facebook">
                <table>
                    <tr>
                        <td>
                            <img src="./img/facebook_icon.png" />
                        </td>

                        <td>
                            Facebook
                        </td>
                    </tr>
                </table>
            </div>

            <div class="contact-item instagram">
                <table>
                    <tr>
                        <td>
                            <img src="./img/instagram_icon.png" />
                        </td>

                        <td>
                            Instagram
                        </td>
                    </tr>
                </table>
            </div>

            <div class="contact-item website">
                <table>
                    <tr>
                        <td>
                            <img src="./img/website_icon.png" />
                        </td>

                        <td>
                            Site
                        </td>
                    </tr>
                </table>
            </div>

            <div class="contact-item linkedin">
                <table>
                    <tr>
                        <td>
                            <img src="./img/linkedin_icon.png" />
                        </td>

                        <td>
                            LinkedIn
                        </td>
                    </tr>
                </table>
            </div>

            <div class="phones-container">
                <span>📞 Telefones:</span>
                <span>(35) 3731-1118</span>
                <span>0800 283 8080</span>
            </div>
        </div>
    </div>
</body>

<footer>
    Copyright © Telemidia Sistemas de Telecomunicação Ltda.
</footer>

</html>
